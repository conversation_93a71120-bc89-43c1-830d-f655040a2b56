<script setup>
import { showModal, showToast } from '@uni-helper/uni-promises'

// 定义组件的 props 和 emits
const props = defineProps({
  progressData: {
    type: Object,
    default: () => ({
      applicationId: 'AP202501080001',
      status: 'reviewing',
      submitTime: '2025-01-08 14:30:00',
      steps: [
        { key: 'submitted', label: '申请提交', status: 'completed', time: '2025-01-08 14:30:00' },
        { key: 'reviewing', label: '材料审核', status: 'current', time: '' },
        { key: 'contract', label: '合同签署', status: 'pending', time: '' },
        { key: 'completed', label: '入住确认', status: 'pending', time: '' },
      ],
    }),
  },
})

const emit = defineEmits(['cancel', 'view-details'])

// 获取当前状态描述
const currentStatusText = computed(() => {
  const currentStep = props.progressData.steps.find(step => step.status === 'current')
  if (!currentStep)
    return '处理中'

  switch (currentStep.key) {
    case 'submitted':
      return '申请已提交，等待审核'
    case 'reviewing':
      return '材料审核中'
    case 'contract':
      return '等待签署合同'
    case 'completed':
      return '等待入住确认'
    default:
      return '处理中'
  }
})

// 获取进度状态样式
function getStepStatusClass(status) {
  switch (status) {
    case 'completed':
      return 'bg-green-500 text-white'
    case 'current':
      return 'bg-primary-500 text-white'
    default:
      return 'bg-gray-200 text-gray-500'
  }
}

// 获取进度状态图标
function getStepStatusIcon(status) {
  switch (status) {
    case 'completed':
      return 'i-carbon-checkmark'
    case 'current':
      return 'i-carbon-time'
    default:
      return 'i-carbon-circle-dash'
  }
}

// 获取步骤说明文本
function getStepDescription(step) {
  if (step.status !== 'current')
    return ''

  switch (step.key) {
    case 'submitted':
      return '您的申请已成功提交，我们将尽快处理'
    case 'reviewing':
      return '正在审核您提交的材料，请耐心等待'
    case 'contract':
      return '材料审核通过，请准备签署入住合同'
    case 'completed':
      return '合同已签署，请按约定时间办理入住手续'
    default:
      return ''
  }
}

// 取消申请
async function handleCancel() {
  try {
    const result = await showModal({
      title: '确认取消',
      content: '确定要取消此申请吗？取消后无法恢复。',
      confirmText: '确定取消',
      cancelText: '我再想想',
    })

    if (result.confirm) {
      emit('cancel')
    }
  }
  catch (error) {
    console.log('用户取消操作')
  }
}

// 查看申请详情
function handleViewDetails() {
  emit('view-details')
}
</script>

<template>
  <view class="application-progress px-4 pb-8">
    <!-- 申请信息 -->
    <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-bold text-gray-800">
          申请信息
        </view>
      </view>

      <view class="p-4 space-y-3">
        <view class="flex justify-between items-center">
          <text class="text-gray-600">
            申请单号
          </text>
          <text class="font-medium text-gray-800">
            {{ progressData.applicationId }}
          </text>
        </view>
        <view class="flex justify-between items-center">
          <text class="text-gray-600">
            申请时间
          </text>
          <text class="font-medium text-gray-800">
            {{ progressData.submitTime }}
          </text>
        </view>
      </view>
    </view>

    <!-- 进度展示 -->
    <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-bold text-gray-800">
          办理进度
        </view>
        <view class="text-sm text-gray-500 mt-1">
          当前状态：{{ currentStatusText }}
        </view>
      </view>

      <view class="p-4">
        <view class="relative">
          <view
            v-for="(step, index) in progressData.steps"
            :key="step.key"
            class="flex items-start space-x-3 relative"
            :class="{ 'mb-6': index < progressData.steps.length - 1 }"
          >
            <!-- 步骤图标 -->
            <view
              class="size-8 rounded-full flex items-center justify-center flex-shrink-0 transition-colors relative z-10"
              :class="getStepStatusClass(step.status)"
            >
              <view
                class="size-4"
                :class="getStepStatusIcon(step.status)"
              ></view>
            </view>

            <!-- 步骤内容 -->
            <view class="flex-1 min-w-0 pt-1">
              <view class="flex items-center justify-between mb-1">
                <text
                  class="font-medium text-sm"
                  :class="step.status === 'current' ? 'text-primary-600' : 'text-gray-800'"
                >
                  {{ step.label }}
                </text>
                <text
                  v-if="step.time"
                  class="text-xs text-gray-500"
                >
                  {{ step.time }}
                </text>
              </view>

              <!-- 当前步骤说明 -->
              <view
                v-if="step.status === 'current'"
                class="text-sm text-gray-600"
              >
                {{ getStepDescription(step) }}
              </view>
            </view>

            <!-- 连接线 -->
            <view
              v-if="index < progressData.steps.length - 1"
              class="absolute left-4 top-8 w-0.5 h-6 bg-gray-200 z-0"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="space-y-3">
      <wd-button
        type="primary"
        size="large"
        block
        @click="handleViewDetails"
      >
        查看申请详情
      </wd-button>

      <wd-button
        type="error"
        size="large"
        block
        plain
        @click="handleCancel"
      >
        取消申请
      </wd-button>
    </view>
  </view>
</template>

<style>
</style>
